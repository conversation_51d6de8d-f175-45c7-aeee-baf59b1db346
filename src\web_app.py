#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
亚马逊图片上传图床工具 - Web版本
提供Web界面和API接口
"""

import os
import sys
import json
import base64
import tempfile
import shutil
import time
import random
import requests
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file, url_for
from flask_cors import CORS
from werkzeug.utils import secure_filename
import concurrent.futures
import threading
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 获取项目根目录
if project_root not in sys.path:
    sys.path.insert(0, project_root)
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入原有的功能模块
try:
    from config.mohe_config import API_KEY, API_URL, FILE_PARAM_NAME, UPLOAD_MODE, UPLOAD_FORMAT
    
    # 优先使用新的模块化结构导入
    try:
        # 确保能找到src目录
        src_dir = os.path.join(project_root, 'src')
        if src_dir not in sys.path:
            sys.path.insert(0, src_dir)
            
        # 从核心模块导入所有需要的功能
        from core import (
            optimize_image, upload_image_to_mohecdn, session, upload_lock,
            save_to_amazon_excel, determine_image_type, extract_asin, 
            validate_image_filename, test_api_connection, ensure_history_dir,
            fill_amazon_template_web, DEFAULT_MAX_WORKERS, DEFAULT_UPLOAD_DELAY, MAX_RETRIES
        )
        
        # 设置默认常量
        ENABLE_IMAGE_OPTIMIZATION = True
        print("✅ 成功从新版模块化结构导入核心功能模块")
        
    except ImportError as import_error:
        print(f"🔍 新版模块化导入失败: {import_error}")
        print(f"🔍 详细错误信息: {import_error.__class__.__name__}")
        import traceback
        traceback.print_exc()
        # 兼容模式：从archive中导入核心模块
        try:
            from archive.duplicate_cleanup.amazon_image_uploader import (
                optimize_image, upload_image_to_mohecdn, save_to_amazon_excel,
                determine_image_type, extract_asin, process_images_for_amazon,
                validate_image_filename, test_api_connection, ensure_history_dir,
                session, upload_lock, DEFAULT_MAX_WORKERS, DEFAULT_UPLOAD_DELAY,
                ENABLE_IMAGE_OPTIMIZATION, MAX_RETRIES
            )
            # 注释掉冲突的导入，使用内置的完整功能版本
            # from archive.duplicate_cleanup.fill_template_web import fill_amazon_template_web
            print("✅ 成功从archive导入核心功能模块（兼容模式）")
        except ImportError:
            print("⚠️ 使用内置功能模块")
            # 内置的基本功能
            def ensure_history_dir():
                """确保历史目录存在"""
                history_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'output', 'results')
                os.makedirs(history_dir, exist_ok=True)
                return history_dir
            
            def validate_image_filename(filename):
                """验证图片文件名"""
                if not filename.startswith('B0'):
                    return False, "文件名必须以B0开头"
                if not any(keyword in filename.upper() for keyword in ['MAIN', 'PT', 'SWCH']):
                    return False, "文件名必须包含MAIN、PT或SWCH关键词"
                return True, "文件名格式正确"
            
            def determine_image_type(filename):
                """确定图片类型"""
                filename_upper = filename.upper()
                if 'MAIN' in filename_upper:
                    return 'MAIN'
                elif 'SWCH' in filename_upper:
                    return 'SWCH'
                elif 'PT' in filename_upper:
                    # 尝试提取PT后的编号
                    import re
                    pt_match = re.search(r'PT(\d+)', filename_upper)
                    if pt_match:
                        pt_num = int(pt_match.group(1))
                        return f'PT{pt_num:02d}'
                    return 'PT01'
                else:
                    return 'OTHER'
            
            def extract_asin(filename):
                """从文件名中提取ASIN"""
                if "_" in filename:
                    parts = filename.split('_')
                    if len(parts) >= 2 and parts[0].startswith('B0'):
                        return parts[0]
                # 如果不是标准格式，尝试正则匹配
                import re
                asin_match = re.search(r'(B0\w{8})', filename)
                if asin_match:
                    return asin_match.group(1)
                return "B000000000"  # 默认ASIN
            
            def optimize_image(file_path):
                """优化图片（内置模式：直接返回原始数据）"""
                try:
                    with open(file_path, 'rb') as f:
                        return f.read()
                except Exception as e:
                    print(f"读取图片文件失败: {e}")
                    return None
            
            def upload_image_to_mohecdn(file_path, custom_name=None, retry_count=0, upload_folder=None):
                """内置上传功能 - 备用实现"""
                print("⚠️ 警告：使用内置上传功能，建议检查模块导入")
                # 作为最后的备用方案，提供基本上传功能
                try:
                    # 使用直接的API调用
                    filename = custom_name if custom_name else os.path.basename(file_path)
                    data = {
                        "api_token": API_KEY,
                        "upload_format": UPLOAD_FORMAT,
                        "mode": UPLOAD_MODE,
                        "protocol_type": "http"
                    }
                    if upload_folder:
                        data["uploadPath"] = upload_folder
                    
                    with open(file_path, 'rb') as file:
                        files = {FILE_PARAM_NAME: (filename, file, 'image/jpeg')}
                        response = requests.post(API_URL, data=data, files=files, timeout=30)
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if 'url' in result:
                                return result['url']
                        except:
                            pass
                        # 尝试从响应文本中提取URL
                        if 'http' in response.text:
                            import re
                            url_match = re.search(r'https?://[^\s\"\']+', response.text)
                            if url_match:
                                return url_match.group(0)
                    return None
                except Exception as e:
                    print(f"内置上传功能失败: {e}")
                    return None
            
            def save_to_amazon_excel(data_dict, output_path, task_list_info=None):
                """内置Excel保存功能"""
                return output_path
            
            def fill_amazon_template_web(template_path, report_path, mapping_path=None, 
                                        product_info_path=None, output_path=None, 
                                        market='US', use_product_info=True):
                """填充亚马逊模板（兼容模式：使用核心功能）"""
                try:
                    # 尝试导入完整的模板填充功能
                    from src.core.template_filler_web import get_amazon_template_web
                    print("🎯 使用核心模块的完整模板填充功能")
                    return get_amazon_template_web(
                        template_path, report_path, mapping_path, 
                        product_info_path, output_path, market, use_product_info
                    )
                except ImportError as import_error:
                    print(f"⚠️ 核心模块导入失败: {import_error}")
                    # 回退到简单的文件复制作为基本功能
                    if output_path:
                        shutil.copy2(template_path, output_path)
                        return True, "模板填充完成（内置基本模式）"
                    return False, "未指定输出路径"
                except Exception as e:
                    print(f"❌ 模板填充过程中出错: {str(e)}")
                    print(f"详细错误信息: {traceback.format_exc()}")
                    return False, f"模板填充失败: {str(e)}"
            
            def test_api_connection():
                """测试API连接"""
                return {"status": "内置模式", "message": "使用内置功能"}
            
            # 设置默认变量
            session = requests.Session() if 'requests' in globals() else None
            upload_lock = threading.Lock()
            DEFAULT_MAX_WORKERS = 3
            DEFAULT_UPLOAD_DELAY = 1
            ENABLE_IMAGE_OPTIMIZATION = True
            MAX_RETRIES = 3
    
    print("✅ 配置和核心功能模块加载完成")
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("⚠️ 请检查项目结构和依赖安装")
    sys.exit(1)

# PyInstaller兼容的路径处理
def get_resource_path(relative_path):
    """获取资源文件的绝对路径，兼容PyInstaller打包环境"""
    try:
        # PyInstaller创建的临时文件夹路径
        base_path = sys._MEIPASS
    except Exception:
        # 开发环境中的路径
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

# 确定模板和静态文件的路径
# 优先使用src/web目录下的模板和静态文件
web_template_path = get_resource_path('src/web/templates')
web_static_path = get_resource_path('src/web/static')

# 如果src/web目录不存在，则使用根目录
if os.path.exists(web_template_path):
    template_path = web_template_path
    static_path = web_static_path
else:
    template_path = get_resource_path('templates')
    static_path = get_resource_path('static')

# 调试信息
print(f"🔍 [Debug] 当前工作目录: {os.getcwd()}")
print(f"🔍 [Debug] 模板路径: {template_path}")
print(f"🔍 [Debug] 静态文件路径: {static_path}")
print(f"🔍 [Debug] 模板文件夹是否存在: {os.path.exists(template_path)}")
print(f"🔍 [Debug] 静态文件夹是否存在: {os.path.exists(static_path)}")

# 创建Flask应用 - 兼容PyInstaller的路径配置
app = Flask(__name__, 
           template_folder=template_path,
           static_folder=static_path)
app.config['SECRET_KEY'] = 'amazon-image-uploader-web-2024'
app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024 * 1024  # 2GB最大文件大小，支持大批量文件上传

# 启用CORS
CORS(app)

# 配置上传文件夹
UPLOAD_FOLDER = 'uploads'
TEMP_FOLDER = 'temp'
HISTORY_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'output', 'results')

# 确保目录存在
for folder in [UPLOAD_FOLDER, TEMP_FOLDER]:
    os.makedirs(folder, exist_ok=True)

ensure_history_dir()

# 支持的图片格式
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

def allowed_file(filename):
    """检查文件扩展名是否被允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def cleanup_temp_files():
    """清理临时文件"""
    try:
        if os.path.exists(TEMP_FOLDER):
            for filename in os.listdir(TEMP_FOLDER):
                file_path = os.path.join(TEMP_FOLDER, filename)
                if os.path.isfile(file_path):
                    os.unlink(file_path)
    except Exception as e:
        print(f"清理临时文件时出错: {e}")

# 全局变量存储上传进度
upload_progress = {}
upload_lock = threading.Lock()

@app.route('/')
def index():
    """主页"""
    try:
        print(f"🔍 [Debug] 尝试渲染index.html模板")
        print(f"🔍 [Debug] Flask app.template_folder: {app.template_folder}")
        
        # 检查模板文件是否存在
        template_file = os.path.join(app.template_folder, 'index.html')
        print(f"🔍 [Debug] 模板文件路径: {template_file}")
        print(f"🔍 [Debug] 模板文件是否存在: {os.path.exists(template_file)}")
        
        if os.path.exists(template_file):
            return render_template('index.html')
        else:
            # 如果模板文件不存在，返回简单的HTML
            return """
            <!DOCTYPE html>
            <html>
            <head>
                <title>亚马逊图片上传图床工具</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>🔧 系统配置错误</h1>
                <p>模板文件未找到，请检查安装包是否完整。</p>
                <p>模板路径: {template_file}</p>
                <p>当前工作目录: {cwd}</p>
                <hr>
                <p>请联系技术支持或重新下载完整的安装包。</p>
            </body>
            </html>
            """.format(template_file=template_file, cwd=os.getcwd())
    except Exception as e:
        print(f"❌ [Error] 渲染主页时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return f"<h1>服务器错误</h1><p>{str(e)}</p>", 500

@app.route('/favicon.ico')
def favicon():
    """返回favicon.ico"""
    return '', 204  # 返回空内容，状态码204 No Content

@app.route('/api/test-connection', methods=['GET'])
def api_test_connection():
    """测试API连接"""
    try:
        result = test_api_connection()
        return jsonify({
            'success': True,
            'result': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/upload-images', methods=['POST'])
def api_upload_images():
    """批量上传图片API"""
    print("[Web调试] === 进入 api_upload_images() API ===")
    try:
        # 检查是否有文件
        print("[Web调试] 检查请求中的文件")
        if 'files' not in request.files:
            print("[Web调试] 请求中没有files字段")
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        files = request.files.getlist('files')
        print(f"[Web调试] 收到 {len(files)} 个文件")
        if not files or all(file.filename == '' for file in files):
            print("[Web调试] 没有有效文件")
            return jsonify({'success': False, 'error': '没有选择有效文件'}), 400
        
        # 获取上传文件夹参数
        upload_folder = request.form.get('upload_folder', 'DEFAULT')
        print(f"[Web调试] 上传文件夹: {upload_folder}")
        
        # 处理图片任务列表文件
        print("[Web调试] 开始处理图片任务列表文件")
        task_list_info = {}
        task_list_file = request.files.get('task_list_file')
        print(f"[Web调试] 任务列表文件: {task_list_file.filename if task_list_file and task_list_file.filename else '未提供'}")
        if task_list_file and task_list_file.filename:
            try:
                import pandas as pd
                
                # 保存任务列表文件到临时目录
                task_filename = secure_filename(task_list_file.filename)
                task_temp_path = os.path.join(TEMP_FOLDER, f"task_list_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{task_filename}")
                task_list_file.save(task_temp_path)
                
                # 读取Excel文件
                task_df = pd.read_excel(task_temp_path)
                print(f"成功读取图片任务列表，共 {len(task_df)} 行")
                
                # 检查必要的列是否存在
                required_cols = ['ASIN', 'SKU', 'MSKU']
                missing_cols = [col for col in required_cols if col not in task_df.columns]
                
                if missing_cols:
                    print(f"图片任务列表缺少必要的列: {missing_cols}")
                    # 尝试查找替代列
                    alt_cols = []
                    for col in task_df.columns:
                        col_lower = str(col).lower()
                        if 'asin' in col_lower:
                            alt_cols.append(('ASIN', col))
                        elif 'sku' in col_lower and 'msku' not in col_lower:
                            alt_cols.append(('SKU', col))
                        elif 'msku' in col_lower:
                            alt_cols.append(('MSKU', col))
                    
                    if alt_cols:
                        print(f"找到可能的替代列: {alt_cols}")
                        for required, found in alt_cols:
                            if required in missing_cols:
                                task_df[required] = task_df[found]
                                missing_cols.remove(required)
                                print(f"使用 {found} 列替代 {required}")
                
                # 创建ASIN到SKU和MSKU的映射
                for _, row in task_df.iterrows():
                    asin = str(row.get('ASIN', '')).strip()
                    if asin:
                        sku = str(row.get('SKU', '')).strip() if 'SKU' in row else ''
                        msku = str(row.get('MSKU', '')).strip() if 'MSKU' in row else ''
                        task_list_info[asin] = {'SKU': sku, 'MSKU': msku}
                
                print(f"成功创建ASIN映射，共 {len(task_list_info)} 个ASIN")
                
                # 清理临时文件
                try:
                    os.unlink(task_temp_path)
                except:
                    pass
                    
            except Exception as e:
                print(f"处理图片任务列表时出错: {str(e)}")
                return jsonify({
                    'success': False, 
                    'error': f'图片任务列表处理失败: {str(e)}'
                }), 400
        
        # 验证文件 - 与桌面版本相同的验证逻辑
        print("[Web调试] 开始验证文件")
        valid_files = []
        invalid_files = []
        
        for file in files:
            print(f"[Web调试] 检查文件: {file.filename}")
            if file and allowed_file(file.filename):
                # 只检查文件名部分，不包括路径
                filename_only = os.path.basename(file.filename)
                print(f"[Web调试] 提取的文件名: {filename_only}")
                is_valid, reason = validate_image_filename(filename_only)
                print(f"[Web调试] 文件名验证结果: {is_valid}, 原因: {reason}")
                if is_valid:
                    valid_files.append(file)
                else:
                    invalid_files.append((file.filename, reason))
            else:
                print(f"[Web调试] 文件类型不被允许: {file.filename}")
                invalid_files.append((file.filename, "不支持的文件类型"))
        
        # 如果有不符合要求的文件，返回详细错误信息（与桌面版本一致）
        print(f"[Web调试] 验证结果 - 有效文件: {len(valid_files)}, 无效文件: {len(invalid_files)}")
        if invalid_files:
            print("[Web调试] 发现无效文件，准备返回错误信息")
            error_msg = "以下图片文件名不符合要求：\n\n"
            for filename, reason in invalid_files[:5]:  # 最多显示5个
                error_msg += f"• {filename}: {reason}\n"
            
            if len(invalid_files) > 5:
                error_msg += f"\n...等共 {len(invalid_files)} 个文件不符合要求。"
            
            error_msg += "\n\n文件名要求：\n1. 必须以B0开头（Amazon ASIN格式）\n2. 必须包含MAIN、PT或SWCH关键词"
            
            if not valid_files:
                print(f"[Web调试] ❌ 没有有效文件，返回400错误")
                print(f"[Web调试] 错误信息: {error_msg}")
                return jsonify({
                    'success': False, 
                    'error': error_msg
                }), 400
        
        if not valid_files:
            print("[Web调试] ❌ 所有文件验证都失败，没有有效的图片文件")
            return jsonify({'success': False, 'error': '没有有效的图片文件'}), 400
        
        # 生成唯一的任务ID
        task_id = f"upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 初始化进度
        with upload_lock:
            upload_progress[task_id] = {
                'total': len(valid_files),
                'completed': 0,
                'failed': 0,
                'results': [],
                'status': 'processing',
                'task_list_info': task_list_info  # 添加任务列表信息
            }
        
        # 在主线程中先保存文件到临时目录，避免文件对象在后台线程中被关闭
        print("[Web调试] 在主线程中保存文件到临时目录")
        temp_files = []
        try:
            for file in valid_files:
                # 只使用文件名的basename部分，不包含路径
                original_filename = os.path.basename(file.filename)
                filename = secure_filename(original_filename)
                # 使用task_id前缀避免文件名冲突，但保持原始文件名用于上传
                temp_path = os.path.join(TEMP_FOLDER, f"{task_id}_{filename}")
                print(f"[Web调试] 保存文件: {original_filename} -> {temp_path}")
                file.save(temp_path)
                # 传递原始文件名而不是带task_id的文件名
                temp_files.append((temp_path, original_filename))
            print(f"[Web调试] 临时文件保存完成，共 {len(temp_files)} 个")
        except Exception as e:
            print(f"[Web调试] 保存临时文件时出错: {e}")
            return jsonify({'success': False, 'error': f'保存文件失败: {str(e)}'}), 500

        # 启动后台上传任务
        print(f"[Web调试] 启动后台上传任务，任务ID: {task_id}")
        threading.Thread(
            target=process_upload_task,
            args=(task_id, temp_files, upload_folder)
        ).start()
        
        print(f"[Web调试] 返回上传成功响应，总文件数: {len(valid_files)}")
        return jsonify({
            'success': True,
            'task_id': task_id,
            'total_files': len(valid_files)
        })
    
    except Exception as e:
        print(f"[Web调试] ❌ api_upload_images 发生异常: {str(e)}")
        print(f"[Web调试] 异常类型: {type(e).__name__}")
        print(f"[Web调试] 异常详情: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': str(e)}), 500

def process_upload_task(task_id, temp_files, upload_folder):
    """处理上传任务（后台线程）- 与桌面版本保持一致"""
    print(f"[Web调试] === 开始处理上传任务 {task_id} ===")
    print(f"[Web调试] 文件数量: {len(temp_files)}, 上传目录: {upload_folder}")
    try:
        # temp_files 已经是 (file_path, filename) 元组的列表
        print(f"[Web调试] 使用已保存的临时文件，共 {len(temp_files)} 个")
        
        # 🚀 超高性能并发设置 - 大幅提升上传速度
        upload_results = []
        # 激进的线程数配置：更多并发，更快速度
        if len(temp_files) <= 10:
            max_workers = min(10, len(temp_files))  # 小批量用10个线程
        elif len(temp_files) <= 50:
            max_workers = min(20, len(temp_files))  # 中批量用20个线程
        else:
            max_workers = min(30, len(temp_files))  # 大批量用30个线程
        
        upload_delay = 0.01  # 极小延迟，最大化速度
        
        print(f"使用高性能并发设置 - 线程数: {max_workers}, 延迟: {upload_delay}秒, 文件数: {len(temp_files)}")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(upload_single_file, file_path, original_name, upload_folder): (file_path, original_name)
                for file_path, original_name in temp_files
            }
            
            for future in concurrent.futures.as_completed(future_to_file):
                file_path, original_name = future_to_file[future]
                try:
                    result = future.result()
                    upload_results.append(result)
                    
                    # 更新进度
                    with upload_lock:
                        if task_id in upload_progress:
                            if result['success']:
                                upload_progress[task_id]['completed'] += 1
                            else:
                                upload_progress[task_id]['failed'] += 1
                            upload_progress[task_id]['results'].append(result)
                    
                except Exception as e:
                    error_result = {
                        'success': False,
                        'filename': original_name,
                        'error': str(e)
                    }
                    upload_results.append(error_result)
                    
                    with upload_lock:
                        if task_id in upload_progress:
                            upload_progress[task_id]['failed'] += 1
                            upload_progress[task_id]['results'].append(error_result)
                
                finally:
                    # 清理临时文件
                    try:
                        if os.path.exists(file_path):
                            os.unlink(file_path)
                    except:
                        pass
                    
                    # 应用与桌面版本相同的上传延迟
                    if upload_delay > 0:
                        time.sleep(upload_delay)
        
        # 生成Excel文件
        try:
            success_results = [r for r in upload_results if r['success']]
            if success_results:
                # 构造数据字典
                data_dict = {}
                for result in success_results:
                    asin = extract_asin(result['filename'])
                    if asin not in data_dict:
                        data_dict[asin] = []
                    
                    data_dict[asin].append({
                        'filename': result['filename'],
                        'url': result['url'],
                        'type': result.get('type', determine_image_type(result['filename']))
                    })
                
                # 生成Excel文件 - 与桌面版本相同的文件名格式
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                excel_filename = f"亚马逊图片模板_{timestamp}.xlsm"
                # 确保历史映射表目录存在
                ensure_history_dir()
                excel_path = os.path.join(HISTORY_FOLDER, excel_filename)
                
                # 获取任务列表信息
                task_list_info = None
                with upload_lock:
                    if task_id in upload_progress:
                        task_list_info = upload_progress[task_id].get('task_list_info', {})
                
                print(f"[Web调试] 调用 save_to_amazon_excel_web() 生成Excel文件")
                save_to_amazon_excel_web(data_dict, excel_path, task_list_info)
                print(f"[Web调试] Excel文件生成完成: {excel_path}")
                
                # 更新进度状态
                with upload_lock:
                    if task_id in upload_progress:
                        upload_progress[task_id]['status'] = 'completed'
                        upload_progress[task_id]['excel_file'] = excel_filename
                        upload_progress[task_id]['excel_path'] = excel_path
                print(f"[Web调试] 任务状态更新为completed")
        
        except Exception as e:
            print(f"生成Excel文件时出错: {e}")
            with upload_lock:
                if task_id in upload_progress:
                    upload_progress[task_id]['status'] = 'completed_with_errors'
                    upload_progress[task_id]['excel_error'] = str(e)
    
    except Exception as e:
        print(f"处理上传任务时出错: {e}")
        with upload_lock:
            if task_id in upload_progress:
                upload_progress[task_id]['status'] = 'failed'
                upload_progress[task_id]['error'] = str(e)

def save_to_amazon_excel_web(data_dict, output_path, task_list_info=None):
    """Web版本：按照亚马逊模板格式保存图片映射数据到Excel文件"""
    print(f"[Web调试] === 进入 save_to_amazon_excel_web() 函数 ===")
    print(f"[Web调试] 输出路径: {output_path}")
    print(f"[Web调试] 数据字典包含 {len(data_dict)} 个ASIN")
    print(f"[Web调试] 任务列表信息: {'已提供' if task_list_info else '未提供'}")
    
    import openpyxl
    from openpyxl.utils import get_column_letter
    
    # 创建新的Excel工作簿和工作表
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "亚马逊图片模板"
    
    # 列标题
    columns = [
        "ASIN", "原文件名", "MSKU", "SKU", "main_image_url", 
        "other_image_url1", "other_image_url2", "other_image_url3", 
        "other_image_url4", "other_image_url5", "other_image_url6", 
        "other_image_url7", "other_image_url8", "swatch_image_url"
    ]
    
    # 添加标题行
    ws.append(columns)
    
    # 设置标题行格式
    for cell in ws[1]:
        cell.font = openpyxl.styles.Font(bold=True)
        cell.alignment = openpyxl.styles.Alignment(horizontal='center')
    
    # 添加数据行
    row_count = 0
    for asin, images in data_dict.items():
        # 🔍 调试信息：开始处理ASIN
        print(f"\n=== 调试信息：开始处理ASIN: {asin} ===")
        print(f"该ASIN的图片数量: {len(images)}")
        
        row = [asin]  # ASIN
        
        # 原文件名 - 使用所有图片的文件名，用逗号分隔
        all_filenames = ', '.join([img['filename'] for img in images if 'filename' in img])
        row.append(all_filenames)
        print(f"所有文件名: {all_filenames}")
        
        # 从任务列表中获取MSKU和SKU（如果提供了任务列表）
        if task_list_info and asin in task_list_info:
            msku = task_list_info[asin].get('MSKU', '')
            sku = task_list_info[asin].get('SKU', '')
        else:
            msku = ''
            sku = ''
        
        # 添加MSKU和SKU列
        row.append(msku)  # MSKU
        row.append(sku)   # SKU
        
        # 默认所有URL为空
        url_columns = [""] * 10  # Main + 8 Others + Swatch
        
        # 🔍 调试信息：显示每张图片的详细信息
        print("\n--- 图片详细信息 ---")
        for i, img in enumerate(images):
            print(f"图片 {i+1}:")
            print(f"  完整数据: {img}")
            print(f"  文件名: {img.get('filename', '未知')}")
            print(f"  类型: {img.get('type', '未设置')}")
            print(f"  URL: {img.get('url', '未设置')}")
        
        # 填充图片URL到对应列
        for img in images:
            if 'type' in img and 'url' in img:
                img_type = img['type']
                url = img['url']
                
                print(f"\n处理图片映射: ASIN={asin}, 类型={img_type}, URL={url[:50]}...")
                
                if img_type == 'MAIN':
                    url_columns[0] = url  # main_image_url
                    print(f"映射到main_image_url: {img_type}")
                elif img_type == 'SWCH':
                    url_columns[9] = url  # swatch_image_url
                    print(f"映射到swatch_image_url: {img_type}")
                elif img_type.startswith('PT') and len(img_type) >= 3:
                    try:
                        # 提取PT后的数字，支持PT1、PT01等格式
                        pt_num_str = img_type[2:].lstrip('0')  # 移除前导0
                        if not pt_num_str:  # 如果是PT00，当作PT0处理
                            pt_num_str = '0'
                        pt_num = int(pt_num_str)
                        if 1 <= pt_num <= 8:
                            # PT01->other_image_url1 (索引1), PT02->other_image_url2 (索引2)
                            url_columns[pt_num] = url  # other_image_url1-8
                            print(f"映射到other_image_url{pt_num}: {img_type}")
                        else:
                            print(f"PT编号超出范围: {pt_num}, 放到第一个空位置")
                            # 如果PT编号超出范围，放在第一个空的other_image_url位置
                            for i in range(1, 9):
                                if not url_columns[i]:
                                    url_columns[i] = url
                                    print(f"放到other_image_url{i}: {img_type}")
                                    break
                    except (ValueError, IndexError) as e:
                        print(f"无法解析PT编号: {img_type}, 错误: {e}")
                        # 如果无法解析PT编号，放在第一个空的other_image_url位置
                        for i in range(1, 9):
                            if not url_columns[i]:
                                url_columns[i] = url
                                print(f"解析失败，放到other_image_url{i}: {img_type}")
                                break
                else:
                    print(f"未知图片类型: {img_type}，放到第一个空位置")
                    # 其他类型图片放在第一个空的位置
                    for i in range(1, 9):
                        if not url_columns[i]:
                            url_columns[i] = url
                            print(f"未知类型，放到other_image_url{i}: {img_type}")
                            break
        
        # 🔍 调试信息：显示最终的URL列表
        print(f"\n--- 最终URL分配结果 ---")
        url_labels = ['主图', '卖点图1', '卖点图2', '卖点图3', '卖点图4', '卖点图5', '卖点图6', '卖点图7', '卖点图8', '色卡图']
        for i, url in enumerate(url_columns):
            if url:
                print(f"  {url_labels[i]}: {url[:50]}...")
            else:
                print(f"  {url_labels[i]}: 空")
        
        # 将URL添加到行中            
        row.extend(url_columns)
        
        # 添加行到工作表
        ws.append(row)
        row_count += 1
        print(f"=== ASIN {asin} 处理完成 ===\n")
    
    # 调整列宽
    column_widths = {
        'A': 15,  # ASIN
        'B': 30,  # 原文件名
        'C': 15,  # MSKU
        'D': 15,  # SKU
    }
    
    # URL列宽度
    for col in range(5, 15):  # E到N列
        column_letter = get_column_letter(col)
        column_widths[column_letter] = 60
    
    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width
    
    # 添加信息表头
    info_sheet = wb.create_sheet(title="信息")
    info_sheet.append(["亚马逊图片映射表"])
    info_sheet.append(["生成时间", datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
    info_sheet.append(["薄荷图床", "图片上传服务"])
    info_sheet.append(["产品数量", str(row_count)])
    info_sheet.append(["图片总数", str(sum(len(imgs) for imgs in data_dict.values()))])
    
    if task_list_info:
        info_sheet.append(["任务列表", f"已使用，包含{len(task_list_info)}个ASIN的信息"])
    else:
        info_sheet.append(["任务列表", "未提供，MSKU和SKU列为空"])
    
    # 保存文件
    wb.save(output_path)
    print(f"亚马逊模板Excel已保存到: {output_path}")
    return output_path

def upload_file_with_custom_name(file_path, custom_name, retry_count=0, upload_folder=None):
    """高性能文件上传，自定义文件名"""
    try:
        _, ext = os.path.splitext(custom_name)
        data = {
            "api_token": API_KEY,
            "upload_format": UPLOAD_FORMAT,
            "mode": UPLOAD_MODE,
            "protocol_type": "http"
        }
        if upload_folder:
            data["uploadPath"] = upload_folder
        else:
            data["uploadPath"] = "DEFAULT"
            
        # 🚀 高性能上传：移除锁，使用连接池并发上传
        with open(file_path, 'rb') as file:
            files = {
                FILE_PARAM_NAME: (custom_name, file, f'image/{ext.lstrip(".").lower()}' if ext.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp'] else 'image/jpeg')
            }
            print(f"开始上传: {custom_name}")
            
            # 移除upload_lock，允许真正的并发上传
            response = session.post(API_URL, data=data, files=files, timeout=30)
        
        # 输出响应状态和内容（调试用）
        print(f"上传响应状态码: {response.status_code}")
        print(f"上传响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"上传响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if 'status' in result and result['status'] == 'error':
                    error_msg = result.get('resultData', '未知错误')
                    print(f"API返回错误: {error_msg}")
                    return None
                    
                if 'status' in result and result['status'] == 'success':
                    if 'url' in result:
                        print(f"上传成功: {result['url']}")
                        return result['url']
                
                if 'url' in result:
                    return result['url']
                elif 'data' in result and isinstance(result['data'], dict) and 'url' in result['data']:
                    return result['data']['url']
                elif 'data' in result and isinstance(result['data'], str) and 'http' in result['data']:
                    return result['data']
                else:
                    if 'http' in response.text:
                        import re
                        url_match = re.search(r'https?://[^\s\"\']+', response.text)
                        if url_match:
                            return url_match.group(0)
                    return None
            except Exception as e:
                print(f"解析响应时出错: {str(e)}")
                if 'http' in response.text:
                    import re
                    url_match = re.search(r'https?://[^\s\"\']+', response.text)
                    if url_match:
                        return url_match.group(0)
                return None
        else:
            print(f"上传失败: {custom_name}, 状态码: {response.status_code}")
            print(f"错误响应: {response.text}")
            
            if response.status_code == 429:
                wait_time = 60 + random.randint(1, 30)
                print(f"达到API速率限制，等待{wait_time}秒...")
                time.sleep(wait_time)
                if retry_count < MAX_RETRIES:
                    print(f"正在重试 ({retry_count+1}/{MAX_RETRIES})...")
                    return upload_file_with_custom_name(file_path, custom_name, retry_count + 1, upload_folder)
            
            return None
            
    except (requests.exceptions.RequestException, requests.exceptions.Timeout, 
            requests.exceptions.ConnectionError, requests.exceptions.SSLError) as e:
        print(f"网络错误: {str(e)}")
        if retry_count < MAX_RETRIES:
            retry_delay = 5 + random.randint(1, 5) * retry_count
            print(f"等待 {retry_delay} 秒后重试 ({retry_count+1}/{MAX_RETRIES})...")
            time.sleep(retry_delay)
            return upload_file_with_custom_name(file_path, custom_name, retry_count + 1, upload_folder)
        else:
            print(f"重试 {MAX_RETRIES} 次后仍然失败")
            return None

def upload_optimized_image_web(image_file, image_data, retry_count=0, upload_folder=None):
    """Web版本：上传优化后的图片数据到图床"""
    try:
        _, ext = os.path.splitext(image_file)
        data = {
            "api_token": API_KEY,
            "upload_format": UPLOAD_FORMAT,
            "mode": UPLOAD_MODE,
            "protocol_type": "http"  # 返回HTTP协议的链接
        }
        if upload_folder:
            data["uploadPath"] = upload_folder
        else:
            data["uploadPath"] = "DEFAULT"  # 默认目录
            
        files = {
            FILE_PARAM_NAME: (image_file, image_data, f'image/{ext.lstrip(".").lower()}' if ext.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp'] else 'image/jpeg')
        }
        print(f"开始上传: {image_file}")
        
        # 🚀 移除upload_lock，允许真正的并发上传
        response = session.post(API_URL, data=data, files=files, timeout=30)
        
        # 输出响应状态和内容（调试用）
        print(f"上传响应状态码: {response.status_code}")
        print(f"上传响应内容: {response.text[:200]}..." if len(response.text) > 200 else f"上传响应内容: {response.text}")
        
        if response.status_code == 200:
            # 尝试解析JSON响应
            try:
                result = response.json()
                
                # 如果是错误响应，检查是否提供了错误详情
                if 'status' in result and result['status'] == 'error':
                    error_msg = result.get('resultData', '未知错误')
                    print(f"API返回错误: {error_msg}")
                    return None
                    
                # 检查成功响应，按照API文档格式解析
                if 'status' in result and result['status'] == 'success':
                    if 'url' in result:
                        print(f"上传成功: {result['url']}")
                        return result['url']
                    else:
                        print("成功响应但未找到URL字段，尝试其他字段")
                
                # 尝试其他可能的响应格式
                if 'url' in result:
                    return result['url']
                elif 'data' in result and isinstance(result['data'], dict) and 'url' in result['data']:
                    return result['data']['url']
                elif 'data' in result and isinstance(result['data'], str) and 'http' in result['data']:
                    return result['data']
                else:
                    print(f"上传成功但无法解析URL: {response.text}")
                    # 如果响应文本中包含URL，直接返回
                    if 'http' in response.text:
                        import re
                        url_match = re.search(r'https?://[^\s\"\']+', response.text)
                        if url_match:
                            return url_match.group(0)
                    return None
            except Exception as e:
                print(f"解析响应时出错: {str(e)}")
                # 尝试从响应文本中提取URL
                if 'http' in response.text:
                    import re
                    url_match = re.search(r'https?://[^\s\"\']+', response.text)
                    if url_match:
                        return url_match.group(0)
                return None
        else:
            print(f"上传失败: {image_file}, 状态码: {response.status_code}")
            print(f"错误响应: {response.text}")
            
            # API 速率限制检查
            if response.status_code == 429:  # Too Many Requests
                wait_time = 60 + random.randint(1, 30)  # 随机等待时间，避免同时重试
                print(f"达到API速率限制，等待{wait_time}秒...")
                time.sleep(wait_time)
                if retry_count < MAX_RETRIES:
                    print(f"正在重试 ({retry_count+1}/{MAX_RETRIES})...")
                    return upload_optimized_image_web(image_file, image_data, retry_count + 1, upload_folder)
            
            return None
            
    except (requests.exceptions.RequestException, requests.exceptions.Timeout, 
            requests.exceptions.ConnectionError, requests.exceptions.SSLError) as e:
        print(f"网络错误: {str(e)}")
        if retry_count < MAX_RETRIES:
            retry_delay = 5 + random.randint(1, 5) * retry_count
            print(f"等待 {retry_delay} 秒后重试 ({retry_count+1}/{MAX_RETRIES})...")
            time.sleep(retry_delay)
            return upload_optimized_image_web(image_file, image_data, retry_count + 1, upload_folder)
        else:
            print(f"重试 {MAX_RETRIES} 次后仍然失败")
            return None

def upload_single_file(file_path, original_name, upload_folder):
    """上传单个文件 - 高性能版本，保持文件名不变"""
    try:
        # 检查是否启用图片优化（与桌面版本一致）
        if ENABLE_IMAGE_OPTIMIZATION:
            # 优化图片并上传优化后的数据
            optimized_data = optimize_image(file_path)
            url = upload_optimized_image_web(original_name, optimized_data, upload_folder=upload_folder)
        else:
            # 高性能直接上传：使用更高效的文件处理方式
            url = upload_file_with_custom_name(file_path, original_name, upload_folder=upload_folder)
        
        if url:
            return {
                'success': True,
                'filename': original_name,
                'url': url,
                'type': determine_image_type(original_name),
                'asin': extract_asin(original_name)
            }
        else:
            return {
                'success': False,
                'filename': original_name,
                'error': '上传失败，未返回有效URL'
            }
    
    except Exception as e:
        return {
            'success': False,
            'filename': original_name,
            'error': str(e)
        }

@app.route('/api/upload-progress/<task_id>')
def api_upload_progress(task_id):
    """获取上传进度"""
    with upload_lock:
        if task_id not in upload_progress:
            return jsonify({'success': False, 'error': '任务不存在'}), 404
        
        return jsonify({
            'success': True,
            'progress': upload_progress[task_id]
        })

@app.route('/api/download-excel/<task_id>')
def api_download_excel(task_id):
    """下载生成的Excel文件"""
    with upload_lock:
        if task_id not in upload_progress:
            return jsonify({'success': False, 'error': '任务不存在'}), 404
        
        progress = upload_progress[task_id]
        if 'excel_path' not in progress:
            return jsonify({'success': False, 'error': 'Excel文件未生成'}), 404
        
        excel_path = progress['excel_path']
        if not os.path.exists(excel_path):
            return jsonify({'success': False, 'error': 'Excel文件不存在'}), 404
        
        return send_file(
            excel_path,
            as_attachment=True,
            download_name=progress['excel_file']
        )

@app.route('/api/parse-urls', methods=['POST'])
def api_parse_urls():
    """解析URL生成映射表"""
    try:
        print("接收到URL解析请求")
        data = request.get_json()
        if not data or 'urls' not in data:
            print("缺少URL数据")
            return jsonify({'success': False, 'error': '缺少URL数据'}), 400
        
        urls = data['urls']
        print(f"收到 {len(urls)} 个URL条目")
        
        if not isinstance(urls, list) or not urls:
            print("URL列表为空或格式错误")
            return jsonify({'success': False, 'error': 'URL列表为空'}), 400
        
        # 处理URL数据
        url_data = {}
        
        for url_info in urls:
            try:
                if isinstance(url_info, dict) and 'filename' in url_info and 'url' in url_info:
                    # 从文件名中提取ASIN
                    filename = url_info['filename']
                    
                    # 如果文件名包含通用ASIN (B000000000)，尝试从URL中提取实际ASIN
                    if "B000000000" in filename:
                        # 尝试从URL中提取ASIN
                        url_parts = url_info['url'].split('/')
                        for part in url_parts:
                            if part.startswith('B0') and '_' in part:
                                # 可能找到了格式为B0XXXXX_PTXX.jpg的ASIN部分
                                asin_part = part.split('_')[0]
                                if len(asin_part) >= 10 and asin_part.startswith('B0'):
                                    # 替换通用ASIN
                                    filename = filename.replace('B000000000', asin_part)
                                    print(f"从URL提取ASIN: {asin_part}, 新文件名: {filename}")
                                    break
                            elif part.startswith('B0') and '.' in part:
                                # 可能找到了格式为B0XXXXX.jpg的ASIN部分
                                asin_part = part.split('.')[0]
                                if len(asin_part) >= 10 and asin_part.startswith('B0'):
                                    # 替换通用ASIN
                                    filename = filename.replace('B000000000', asin_part)
                                    print(f"从URL提取ASIN: {asin_part}, 新文件名: {filename}")
                                    break
                    
                    # 提取ASIN - 使用改进的提取逻辑
                    asin = None
                    
                    # 方法1：直接从文件名中提取ASIN
                    if "_" in filename:
                        parts = filename.split('_')
                        if len(parts) >= 2 and parts[0].startswith('B0'):
                            asin = parts[0]
                    
                    # 方法2：如果文件名中有B0但不是标准格式
                    if not asin:
                        import re
                        asin_match = re.search(r'(B0\w{8})', filename)
                        if asin_match:
                            asin = asin_match.group(1)
                    
                    # 方法3：使用extract_asin函数
                    if not asin:
                        asin = extract_asin(filename)
                    
                    # 如果仍然没有ASIN，使用默认值
                    if not asin or asin == "B000000000":
                        print(f"警告：无法从文件名中提取有效ASIN: {filename}, URL: {url_info['url']}")
                        # 尝试从URL中提取
                        import re
                        asin_match = re.search(r'(B0\w{8})', url_info['url'])
                        if asin_match:
                            asin = asin_match.group(1)
                            print(f"从URL中提取到ASIN: {asin}")
                        else:
                            # 使用URL的哈希作为标识
                            import hashlib
                            url_hash = hashlib.md5(url_info['url'].encode()).hexdigest()[:8]
                            asin = f"B0{url_hash.upper()}"
                            print(f"生成URL哈希作为ASIN: {asin}")
                    
                    # 添加到数据字典
                    if asin not in url_data:
                        url_data[asin] = []
                    
                    # 确定图片类型
                    img_type = determine_image_type(filename)
                    if not img_type or img_type == 'OTHER':
                        # 如果无法确定类型，使用改进的识别逻辑
                        filename_upper = filename.upper()
                        if 'MAIN' in filename_upper:
                            img_type = 'MAIN'
                        elif 'SWCH' in filename_upper:
                            img_type = 'SWCH'
                        elif 'PT' in filename_upper:
                            # 尝试提取PT后的编号，支持多种格式
                            import re
                            # 匹配PT后面跟数字的模式：PT1, PT01, PT02等
                            pt_match = re.search(r'PT(\d+)', filename_upper)
                            if pt_match:
                                pt_num = int(pt_match.group(1))
                                if 1 <= pt_num <= 8:
                                    img_type = f'PT{pt_num:02d}'  # 格式化为PT01, PT02等
                                else:
                                    img_type = 'PT01'  # 超出范围的默认为PT01
                            else:
                                img_type = 'PT01'  # 无法解析数字时默认为PT01
                        else:
                            img_type = 'MAIN'  # 完全无法识别时默认为MAIN
                    
                    print(f"处理URL: ASIN={asin}, 类型={img_type}, 文件名={filename}")
                    
                    url_data[asin].append({
                        'filename': filename,
                        'url': url_info['url'],
                        'type': img_type
                    })
            except Exception as item_error:
                print(f"处理单个URL时出错: {str(item_error)}")
                # 继续处理下一个URL
                continue
        
        if not url_data:
            print("处理后没有有效的URL数据")
            return jsonify({'success': False, 'error': '没有有效的URL数据'}), 400
        
        print(f"成功处理 {len(url_data)} 个ASIN的URL数据")
        
        # 生成Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_filename = f"url_mapping_{timestamp}.xlsm"
        excel_path = os.path.join(HISTORY_FOLDER, excel_filename)
        
        # 确保历史目录存在
        ensure_history_dir()
        
        # 使用增强版save_to_amazon_excel，支持任务列表信息
        save_to_amazon_excel_with_task_list(url_data, excel_path, task_list_info, title="URL映射表")
        print(f"Excel文件生成完成: {excel_path}")
        
        return jsonify({
            'success': True,
            'message': 'Excel映射表生成成功',
            'excel_file': excel_filename,
            'total_asins': len(url_data),
            'total_urls': len(urls),
            'task_list_used': len(task_list_info) > 0
        })
    
    except Exception as e:
        print(f"生成Excel时出错: {str(e)}")
        return jsonify({'success': False, 'error': f'处理失败: {str(e)}'}), 500

@app.route('/api/debug-history-path')
def api_debug_history_path():
    """调试历史文件夹路径"""
    try:
        current_dir = os.getcwd()
        history_path = os.path.abspath(HISTORY_FOLDER)
        exists = os.path.exists(HISTORY_FOLDER)
        
        debug_info = {
            'current_dir': current_dir,
            'history_folder_relative': HISTORY_FOLDER,
            'history_folder_absolute': history_path,
            'exists': exists
        }
        
        if exists:
            all_files = os.listdir(HISTORY_FOLDER)
            xlsx_files = [f for f in all_files if f.endswith(('.xlsx', '.xlsm'))]
            debug_info['all_files'] = all_files
            debug_info['xlsx_files'] = xlsx_files
        
        return jsonify({'success': True, 'debug': debug_info})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/history-files')
def api_history_files():
    """获取历史文件列表"""
    try:
        if not os.path.exists(HISTORY_FOLDER):
            return jsonify({'success': True, 'files': []})
        
        files = []
        for filename in os.listdir(HISTORY_FOLDER):
            if filename.endswith(('.xlsx', '.xlsm')):
                file_path = os.path.join(HISTORY_FOLDER, filename)
                file_stat = os.stat(file_path)
                files.append({
                    'filename': filename,
                    'size': file_stat.st_size,
                    'modified': datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # 按修改时间排序
        files.sort(key=lambda x: x['modified'], reverse=True)
        
        return jsonify({'success': True, 'files': files})
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/download-history/<filename>')
def api_download_history(filename):
    """下载历史文件"""
    try:
        # 安全检查文件名
        filename = secure_filename(filename)
        file_path = os.path.join(HISTORY_FOLDER, filename)
        
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404
        
        return send_file(file_path, as_attachment=True, download_name=filename)
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/config')
def api_config():
    """获取当前配置信息"""
    try:
        return jsonify({
            'success': True,
            'config': {
                'api_url': API_URL,
                'api_key': API_KEY[:8] + '...' if len(API_KEY) > 8 else API_KEY,
                'upload_mode': UPLOAD_MODE,
                'upload_format': UPLOAD_FORMAT
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/fill-template', methods=['POST'])
def api_fill_template():
    """填充亚马逊模板功能"""
    print("\n" + "="*50)
    print("🚀 收到填充模板请求！")
    print("="*50)
    
    # 初始化临时文件路径变量
    template_path = None
    report_path = None
    mapping_path = None
    product_info_path = None
    
    try:
        # 获取上传的文件和参数 - 增加详细调试信息
        print(f"📋 请求文件列表: {list(request.files.keys())}")
        print(f"📋 请求表单参数: {list(request.form.keys())}")
        
        # 检查每个必要文件
        missing_files = []
        if 'template_file' not in request.files:
            missing_files.append('template_file')
        if 'report_file' not in request.files:
            missing_files.append('report_file')
        if 'mapping_file' not in request.files:
            missing_files.append('mapping_file')
            
        if missing_files:
            error_msg = f"缺少必要文件: {', '.join(missing_files)}"
            print(f"❌ 错误：{error_msg}")
            return jsonify({'success': False, 'error': f'请上传{error_msg}'}), 400
        
        template_file = request.files['template_file']
        report_file = request.files['report_file']
        mapping_file = request.files['mapping_file']  # 必选的映射文件
        product_info_file = request.files.get('product_info_file')  # 可选的产品资料文件
        
        # 获取市场参数
        market = request.form.get('market', 'US')  # 默认US市场
        use_product_info = request.form.get('use_product_info', 'false').lower() == 'true'
        
        if template_file.filename == '' or report_file.filename == '' or mapping_file.filename == '':
            return jsonify({'success': False, 'error': '请选择有效的模板文件、报告文件和图片映射文件'}), 400
        
        # 保存文件到临时目录
        template_path = os.path.join(TEMP_FOLDER, 'template_' + secure_filename(template_file.filename))
        report_path = os.path.join(TEMP_FOLDER, 'report_' + secure_filename(report_file.filename))
        mapping_path = os.path.join(TEMP_FOLDER, 'mapping_' + secure_filename(mapping_file.filename))
        
        template_file.save(template_path)
        report_file.save(report_path)
        mapping_file.save(mapping_path)
        
        if product_info_file and product_info_file.filename != '':
            product_info_path = os.path.join(TEMP_FOLDER, 'product_info_' + secure_filename(product_info_file.filename))
            product_info_file.save(product_info_path)
        
        # 调用填充模板的核心逻辑
        result_filename = f"filled_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsm"
        result_path = os.path.join(HISTORY_FOLDER, result_filename)
        
        # 调用核心处理函数
        success, message = fill_amazon_template_web(
            template_path,
            report_path,
            mapping_path,
            product_info_path,
            result_path,
            market,
            use_product_info
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'result_file': result_filename,
                'download_url': f'/api/download/{result_filename}',
                'files': [{
                    'output_name': result_filename,
                    'original_name': template_file.filename,
                    'download_url': f'/api/download/{result_filename}'
                }]
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 500
        
    except Exception as e:
        print(f"填充模板错误: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        # 清理临时文件
        temp_files = []
        if template_path:
            temp_files.append(template_path)
        if report_path:
            temp_files.append(report_path)
        if mapping_path:
            temp_files.append(mapping_path)
        if product_info_path:
            temp_files.append(product_info_path)
            
        for temp_file in temp_files:
            try:
                if temp_file and os.path.exists(temp_file):
                    os.unlink(temp_file)
                    print(f"🗑️ 已清理临时文件: {temp_file}")
            except Exception as e:
                print(f"⚠️ 清理临时文件失败 {temp_file}: {e}")
                pass

@app.route('/api/test-report', methods=['POST'])
def api_test_report():
    """测试商品分类报告读取"""
    try:
        if 'report_file' not in request.files:
            return jsonify({'success': False, 'error': '请上传报告文件'}), 400
        
        report_file = request.files['report_file']
        if report_file.filename == '':
            return jsonify({'success': False, 'error': '请选择有效的文件'}), 400
        
        # 保存文件到临时目录
        report_path = os.path.join(TEMP_FOLDER, 'test_report_' + secure_filename(report_file.filename))
        report_file.save(report_path)
        
        try:
            # 读取Excel文件进行测试
            import pandas as pd
            
            # 尝试读取不同的sheet
            xl_file = pd.ExcelFile(report_path)
            sheet_names = xl_file.sheet_names
            
            test_results = {
                'file_name': report_file.filename,
                'sheet_names': sheet_names,
                'total_sheets': len(sheet_names),
                'sheets_data': {}
            }
            
            # 读取每个sheet的基本信息
            for sheet_name in sheet_names[:5]:  # 最多读取前5个sheet
                try:
                    df = pd.read_excel(report_path, sheet_name=sheet_name)
                    test_results['sheets_data'][sheet_name] = {
                        'rows': len(df),
                        'columns': len(df.columns),
                        'column_names': df.columns.tolist()[:10]  # 最多显示前10列
                    }
                except Exception as e:
                    test_results['sheets_data'][sheet_name] = {
                        'error': str(e)
                    }
            
            return jsonify({
                'success': True,
                'test_results': test_results
            })
            
        finally:
            # 清理临时文件
            try:
                if os.path.exists(report_path):
                    os.unlink(report_path)
            except:
                pass
                
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/download/<filename>')
def api_download_file(filename):
    """下载生成的文件"""
    try:
        # 安全检查文件名
        safe_filename = secure_filename(filename)
        file_path = os.path.join(HISTORY_FOLDER, safe_filename)
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404
        
        # 检查文件是否在允许的目录内
        if not os.path.abspath(file_path).startswith(os.path.abspath(HISTORY_FOLDER)):
            return jsonify({'success': False, 'error': '无效的文件路径'}), 403
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=safe_filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.errorhandler(413)
def file_too_large(e):
    """文件过大错误处理"""
    return jsonify({
        'success': False, 
        'error': '文件过大，请选择小于200MB的文件。如果单个图片超过10MB，建议先压缩后再上传。'
    }), 413

@app.errorhandler(404)
def not_found_error(e):
    """404错误处理 - 忽略静态文件映射错误"""
    error_msg = str(e)
    
    # 对于.map文件的404错误，静默处理
    if '.map' in request.path:
        return '', 404
    
    # 对于其他404错误，返回友好信息
    print(f"404错误: {request.path}")
    return jsonify({'success': False, 'error': '请求的资源不存在'}), 404

@app.errorhandler(Exception)
def handle_exception(e):
    """全局异常处理"""
    print(f"服务器错误: {e}")
    print(traceback.format_exc())
    return jsonify({'success': False, 'error': '服务器内部错误'}), 500

# 清理函数
def cleanup_old_progress():
    """清理旧的进度记录"""
    with upload_lock:
        current_time = datetime.now()
        to_remove = []
        for task_id, progress in upload_progress.items():
            # 清理1小时前的记录
            if hasattr(progress, 'created_time'):
                time_diff = current_time - progress['created_time']
                if time_diff.total_seconds() > 3600:  # 1小时
                    to_remove.append(task_id)
        
        for task_id in to_remove:
            del upload_progress[task_id]

@app.route('/api/generate-url-excel', methods=['POST'])
def api_generate_url_excel():
    """生成URL映射Excel文件 - 支持任务列表文件"""
    try:
        print("接收到Excel生成请求")
        
        # 获取解析后的URL数据
        urls_json = request.form.get('urls')
        if not urls_json:
            print("缺少URL数据")
            return jsonify({'success': False, 'error': '缺少URL数据'}), 400
            
        urls = json.loads(urls_json)
        print(f"收到 {len(urls)} 个URL条目")
        
        if not isinstance(urls, list) or not urls:
            print("URL列表为空或格式错误")
            return jsonify({'success': False, 'error': 'URL列表为空'}), 400
        
        # 处理任务列表文件（如果提供了）
        task_list_info = {}
        task_list_file = request.files.get('task_list_file')
        
        if task_list_file:
            try:
                print(f"处理任务列表文件: {task_list_file.filename}")
                
                # 保存临时文件
                temp_path = os.path.join(UPLOAD_FOLDER, f"temp_task_list_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                task_list_file.save(temp_path)
                
                # 读取Excel文件
                import pandas as pd
                task_df = pd.read_excel(temp_path)
                print(f"成功读取任务列表，共 {len(task_df)} 行")
                
                # 检查必要的列是否存在
                required_cols = ['ASIN', 'SKU', 'MSKU']
                missing_cols = [col for col in required_cols if col not in task_df.columns]
                
                if missing_cols:
                    print(f"任务列表缺少必要的列: {missing_cols}")
                    # 尝试查找替代列
                    alt_cols = []
                    for col in task_df.columns:
                        col_lower = str(col).lower()
                        if 'asin' in col_lower:
                            alt_cols.append(('ASIN', col))
                        elif 'sku' in col_lower and 'msku' not in col_lower:
                            alt_cols.append(('SKU', col))
                        elif 'msku' in col_lower:
                            alt_cols.append(('MSKU', col))
                    
                    if alt_cols:
                        print(f"找到可能的替代列: {alt_cols}")
                        for required, found in alt_cols:
                            if required in missing_cols:
                                task_df[required] = task_df[found]
                                missing_cols.remove(required)
                                print(f"使用 {found} 列替代 {required}")
                
                # 创建ASIN到SKU和MSKU的映射
                for _, row in task_df.iterrows():
                    asin = str(row.get('ASIN', '')).strip()
                    if asin:
                        sku = str(row.get('SKU', '')).strip() if 'SKU' in row else ''
                        msku = str(row.get('MSKU', '')).strip() if 'MSKU' in row else ''
                        task_list_info[asin] = {'SKU': sku, 'MSKU': msku}
                
                print(f"成功创建ASIN映射，共 {len(task_list_info)} 个ASIN")
                
                # 删除临时文件
                os.remove(temp_path)
                
            except Exception as e:
                print(f"处理任务列表文件时出错: {str(e)}")
                # 不阻止继续处理，只是不使用任务列表信息
        
        # 处理URL数据，按ASIN分组
        url_data = {}
        
        for url_info in urls:
            try:
                if isinstance(url_info, dict) and 'filename' in url_info and 'url' in url_info:
                    filename = url_info['filename']
                    asin = None
                    
                    # 方法1：直接从文件名中提取ASIN
                    if "_" in filename:
                        parts = filename.split('_')
                        if len(parts) >= 2 and parts[0].startswith('B0'):
                            asin = parts[0]
                    
                    # 方法2：如果文件名中有B0但不是标准格式
                    if not asin:
                        import re
                        asin_match = re.search(r'(B0\w{8})', filename)
                        if asin_match:
                            asin = asin_match.group(1)
                    
                    # 方法3：使用extract_asin函数
                    if not asin:
                        asin = extract_asin(filename)
                    
                    # 如果仍然没有ASIN，使用默认值
                    if not asin or asin == "B000000000":
                        print(f"警告：无法从文件名中提取有效ASIN: {filename}, URL: {url_info['url']}")
                        # 尝试从URL中提取
                        import re
                        asin_match = re.search(r'(B0\w{8})', url_info['url'])
                        if asin_match:
                            asin = asin_match.group(1)
                            print(f"从URL中提取到ASIN: {asin}")
                        else:
                            # 使用URL的哈希作为标识
                            import hashlib
                            url_hash = hashlib.md5(url_info['url'].encode()).hexdigest()[:8]
                            asin = f"B0{url_hash.upper()}"
                            print(f"生成URL哈希作为ASIN: {asin}")
                    
                    # 添加到数据字典
                    if asin not in url_data:
                        url_data[asin] = []
                    
                    # 确定图片类型
                    img_type = determine_image_type(filename)
                    if not img_type or img_type == 'OTHER':
                        # 如果无法确定类型，使用改进的识别逻辑
                        filename_upper = filename.upper()
                        if 'MAIN' in filename_upper:
                            img_type = 'MAIN'
                        elif 'SWCH' in filename_upper:
                            img_type = 'SWCH'
                        elif 'PT' in filename_upper:
                            # 尝试提取PT后的编号，支持多种格式
                            import re
                            # 匹配PT后面跟数字的模式：PT1, PT01, PT02等
                            pt_match = re.search(r'PT(\d+)', filename_upper)
                            if pt_match:
                                pt_num = int(pt_match.group(1))
                                if 1 <= pt_num <= 8:
                                    img_type = f'PT{pt_num:02d}'  # 格式化为PT01, PT02等
                                else:
                                    img_type = 'PT01'  # 超出范围的默认为PT01
                            else:
                                img_type = 'PT01'  # 无法解析数字时默认为PT01
                        else:
                            img_type = 'MAIN'  # 完全无法识别时默认为MAIN
                    
                    print(f"处理URL: ASIN={asin}, 类型={img_type}, 文件名={filename}")
                    
                    url_data[asin].append({
                        'filename': filename,
                        'url': url_info['url'],
                        'type': img_type
                    })
            except Exception as item_error:
                print(f"处理单个URL时出错: {str(item_error)}")
                # 继续处理下一个URL
                continue
        
        if not url_data:
            print("处理后没有有效的URL数据")
            return jsonify({'success': False, 'error': '没有有效的URL数据'}), 400
        
        print(f"成功处理 {len(url_data)} 个ASIN的URL数据")
        
        # 生成Excel文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_filename = f"url_mapping_{timestamp}.xlsm"
        excel_path = os.path.join(HISTORY_FOLDER, excel_filename)
        
        # 确保历史目录存在
        ensure_history_dir()
        
        # 使用增强版save_to_amazon_excel，支持任务列表信息
        save_to_amazon_excel_with_task_list(url_data, excel_path, task_list_info, title="URL映射表")
        print(f"Excel文件生成完成: {excel_path}")
        
        return jsonify({
            'success': True,
            'message': 'Excel映射表生成成功',
            'excel_file': excel_filename,
            'total_asins': len(url_data),
            'total_urls': len(urls),
            'task_list_used': len(task_list_info) > 0
        })
        
    except Exception as e:
        print(f"生成Excel时出错: {str(e)}")
        return jsonify({'success': False, 'error': f'处理失败: {str(e)}'}), 500

def save_to_amazon_excel_with_task_list(data_dict, output_path, task_list_info=None, title="亚马逊图片映射表"):
    """
    保存亚马逊图片映射为Excel文件 - 增强版，支持任务列表信息
    
    参数:
        data_dict: 按ASIN分组的图片数据字典
        output_path: 输出Excel文件路径
        task_list_info: 任务列表信息字典 {ASIN: {'SKU': '', 'MSKU': ''}}
        title: Excel标题
    """
    try:
        import openpyxl
        from openpyxl.utils import get_column_letter
        from openpyxl.styles import Font, Alignment, PatternFill
        
        print(f"开始生成增强版Excel文件: {output_path}")
        
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = title
        
        # 设置标题样式
        title_font = Font(name='Arial', size=14, bold=True, color='FFFFFF')
        title_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        title_alignment = Alignment(horizontal='center', vertical='center')
        
        # 定义列标题（与桌面版本保持一致）
        headers = [
            "店铺", "ASIN", "原文件名", "MSKU", "SKU", "main_image_url",
            "other_image_url1", "other_image_url2", "other_image_url3",
            "other_image_url4", "other_image_url5", "other_image_url6",
            "other_image_url7", "other_image_url8", "swatch_image_url"
        ]
        
        # 添加标题行
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = title_font
            cell.fill = title_fill
            cell.alignment = title_alignment
        
        # 处理数据
        row_count = 1
        for asin, images in data_dict.items():
            row_count += 1
            
            # 从URL推断店铺名
            shop = "DEFAULT"
            if images and 'url' in images[0]:
                url_parts = images[0]['url'].split('/')
                if len(url_parts) >= 3:
                    # 尝试从URL中提取店铺信息
                    shop = url_parts[-2] if url_parts[-2] else "DEFAULT"
            
            # 收集所有文件名
            all_filenames = ', '.join([img.get('filename', '') for img in images if 'filename' in img])
            
            # 从任务列表中获取MSKU和SKU
            msku = ""
            sku = ""
            if task_list_info and asin in task_list_info:
                msku = task_list_info[asin].get('MSKU', '')
                sku = task_list_info[asin].get('SKU', '')
            
            # 创建行数据
            row_data = [shop, asin, all_filenames, msku, sku]
            
            # 初始化URL列（Main + 8 Others + Swatch）
            url_columns = [""] * 10
            
            # 填充URL数据
            for img in images:
                if 'type' in img and 'url' in img:
                    img_type = img['type']
                    url = img['url']
                    
                    print(f"处理图片映射: ASIN={asin}, 类型={img_type}, URL={url[:50]}...")
                    
                    if img_type == 'MAIN':
                        url_columns[0] = url  # main_image_url
                        print(f"映射到main_image_url: {img_type}")
                    elif img_type == 'SWCH':
                        url_columns[9] = url  # swatch_image_url
                        print(f"映射到swatch_image_url: {img_type}")
                    elif img_type.startswith('PT') and len(img_type) >= 3:
                        try:
                            # 提取PT后的数字，支持PT1、PT01等格式
                            pt_num_str = img_type[2:].lstrip('0')  # 移除前导0
                            if not pt_num_str:  # 如果是PT00，当作PT0处理
                                pt_num_str = '0'
                            pt_num = int(pt_num_str)
                            if 1 <= pt_num <= 8:
                                # PT01->other_image_url1 (索引1), PT02->other_image_url2 (索引2)
                                url_columns[pt_num] = url  # other_image_url1-8
                                print(f"映射到other_image_url{pt_num}: {img_type}")
                            else:
                                print(f"PT编号超出范围: {pt_num}, 放到第一个空位置")
                                # 如果PT编号超出范围，放在第一个空的other_image_url位置
                                for i in range(1, 9):
                                    if not url_columns[i]:
                                        url_columns[i] = url
                                        print(f"放到other_image_url{i}: {img_type}")
                                        break
                        except (ValueError, IndexError) as e:
                            print(f"无法解析PT编号: {img_type}, 错误: {e}")
                            # 如果无法解析PT编号，放在第一个空的other_image_url位置
                            for i in range(1, 9):
                                if not url_columns[i]:
                                    url_columns[i] = url
                                    print(f"解析失败，放到other_image_url{i}: {img_type}")
                                    break
                    else:
                        print(f"未知图片类型: {img_type}，放到第一个空位置")
                        # 其他类型图片放在第一个空的位置
                        for i in range(1, 9):
                            if not url_columns[i]:
                                url_columns[i] = url
                                print(f"未知类型，放到other_image_url{i}: {img_type}")
                                break
            
            # 将URL添加到行中            
            row_data.extend(url_columns)
            
            # 添加行到工作表
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_count, column=col_idx, value=value)
        
        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if cell.value and len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # 保存文件
        wb.save(output_path)
        print(f"增强版Excel文件保存成功: {output_path}，共 {row_count-1} 行数据")
        
        # 显示统计信息
        task_list_used = len(task_list_info) if task_list_info else 0
        print(f"统计信息: ASIN数量={len(data_dict)}, 任务列表映射={task_list_used}")
        
        return True
        
    except Exception as e:
        print(f"保存增强版Excel文件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# 导入路由模块
try:
    from src.web.routes.rename_routes import register_rename_routes
    # 注册重命名路由
    register_rename_routes(app)
    print("✅ 成功注册图片重命名功能路由")
except ImportError:
    print("⚠️ 图片重命名路由模块导入失败")

if __name__ == '__main__':
    print("🚀 启动亚马逊图片上传图床工具 Web版本...")
    print(f"⚙️ API配置: {API_URL}")
    
    # 清理临时文件
    cleanup_temp_files()
    
    # 使用动态端口管理器启动应用
    try:
        # 优先从新的模块化结构导入
        from src.core.port_manager import create_flask_port_manager
        print("✅ 使用新版模块化端口管理器")
        
        # 创建端口管理器
        port_manager = create_flask_port_manager(
            app_name="亚马逊图片上传图床工具", 
            preferred_port=5000
        )
        
        # 使用动态端口启动
        port_manager.run_with_dynamic_port(
            app,
            host='0.0.0.0',
            debug=True,
            threaded=True,
            use_reloader=False  # 禁用重载器避免双重启动
        )
        
    except ImportError as e:
        # 兼容性处理：如果端口管理器不可用，使用传统方式
        print(f"⚠️ 动态端口管理器不可用: {e}")
        print("🔄 使用默认端口 5000")
        print(f"🌐 访问地址: http://localhost:5000")
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            threaded=True
        )