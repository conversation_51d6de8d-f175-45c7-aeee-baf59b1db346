# -*- coding: utf-8 -*-
"""
Excel处理模块 - 简化版本
只使用openpyxl，不依赖pandas，适用于简化打包版本
"""

import os
import openpyxl
import logging
from datetime import datetime
from openpyxl.styles import Font, Alignment
from .utils import ensure_dir_exists, get_timestamp

# 设置日志
logger = logging.getLogger(__name__)

class ExcelProcessorMinimal:
    """Excel文件处理器 - 简化版本"""
    
    def __init__(self):
        """初始化Excel处理器"""
        pass
    
    def save_amazon_mapping(self, data_dict, output_path, task_list_info=None, title="亚马逊图片映射表"):
        """
        按照亚马逊模板格式保存图片映射数据到Excel文件
        
        参数:
            data_dict (dict): 图片数据字典 {ASIN: [图片信息列表]}
            output_path (str): 输出文件路径
            task_list_info (dict, optional): 任务列表信息
            title (str): 工作表标题
            
        返回:
            bool: 保存是否成功
        """
        try:
            # 确保输出目录存在
            ensure_dir_exists(os.path.dirname(output_path))
            
            # 创建新的Excel工作簿和工作表
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "亚马逊图片模板"
            
            # 列标题
            columns = [
                "ASIN", "原文件名", "MSKU", "SKU", "main_image_url", 
                "other_image_url1", "other_image_url2", "other_image_url3", 
                "other_image_url4", "other_image_url5", "other_image_url6", 
                "other_image_url7", "other_image_url8", "swatch_image_url"
            ]
            
            # 添加标题行
            ws.append(columns)
            
            # 设置标题行格式
            for cell in ws[1]:
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
            
            # 添加数据行
            row_count = 0
            for asin, images in data_dict.items():
                row = [asin]  # ASIN
                
                # 原文件名 - 使用所有图片的文件名，用逗号分隔
                all_filenames = ', '.join([img['filename'] for img in images if 'filename' in img])
                row.append(all_filenames)
                
                # 从任务列表中获取MSKU和SKU
                if task_list_info:
                    msku = task_list_info.get(asin, {}).get('MSKU', '')
                    sku = task_list_info.get(asin, {}).get('SKU', '')
                else:
                    msku = ''
                    sku = ''
                
                # 添加MSKU和SKU列
                row.append(msku)  # MSKU
                row.append(sku)   # SKU
                
                # 默认所有URL为空
                url_columns = [""] * 10  # Main + 8 Others + Swatch
                
                # 填充图片URL到对应列
                for img in images:
                    if 'type' in img and 'url' in img:
                        img_type = img['type']
                        url = img['url']
                        
                        if img_type == 'MAIN':
                            url_columns[0] = url  # Main Image URL
                        elif img_type == 'SWCH':
                            url_columns[9] = url  # Swatch Image URL
                        elif img_type.startswith('PT') and len(img_type) == 4:
                            try:
                                # 提取PT后的数字，如PT01中的1
                                pt_num = int(img_type[2:])
                                if 1 <= pt_num <= 8:
                                    url_columns[pt_num] = url  # Other Image URL1-8
                            except ValueError:
                                # 如果无法解析数字，忽略
                                pass
                
                # 将URL添加到行中            
                row.extend(url_columns)
                
                # 添加行到工作表
                ws.append(row)
                row_count += 1
            
            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = openpyxl.utils.get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存文件
            wb.save(output_path)
            logger.info(f"成功保存Excel文件: {output_path}，共 {row_count} 行数据")
            return True
            
        except Exception as e:
            logger.error(f"保存Excel文件失败: {str(e)}")
            return False
    
    def parse_urls_to_excel(self, url_mappings, output_path):
        """
        将URL映射转换为Excel文件
        
        参数:
            url_mappings (list): URL映射列表 [{'filename': '', 'url': ''}, ...]
            output_path (str): 输出文件路径
            
        返回:
            bool: 转换是否成功
        """
        try:
            # 处理URL映射数据
            data_dict = {}
            
            for mapping in url_mappings:
                filename = mapping.get('filename', '')
                url = mapping.get('url', '')
                
                if not filename or not url:
                    continue
                
                # 从文件名提取信息
                from .utils import extract_asin, determine_image_type
                asin = extract_asin(filename)
                image_type = determine_image_type(filename)
                
                if not asin:
                    logger.warning(f"无法从文件名 {filename} 中提取ASIN，跳过")
                    continue
                
                # 保存到数据字典
                if asin not in data_dict:
                    data_dict[asin] = []
                
                data_dict[asin].append({
                    'filename': filename,
                    'type': image_type,
                    'url': url
                })
            
            # 保存为Excel文件
            return self.save_amazon_mapping(data_dict, output_path, title="URL映射表")
            
        except Exception as e:
            logger.error(f"解析URL映射失败: {str(e)}")
            return False
    
    def read_excel_basic(self, file_path, sheet_name=None):
        """
        基础Excel读取功能（使用openpyxl）
        
        参数:
            file_path (str): Excel文件路径
            sheet_name (str, optional): 工作表名称
            
        返回:
            list: 读取的数据行列表，失败返回None
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return None
            
            # 打开工作簿
            wb = openpyxl.load_workbook(file_path, read_only=True)
            
            # 选择工作表
            if sheet_name:
                if sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                else:
                    logger.error(f"工作表 '{sheet_name}' 不存在")
                    return None
            else:
                ws = wb.active
            
            # 读取所有行
            data = []
            for row in ws.iter_rows(values_only=True):
                # 过滤空行
                if any(cell is not None for cell in row):
                    data.append(list(row))
            
            logger.info(f"成功读取Excel文件: {file_path}，共 {len(data)} 行数据")
            return data
            
        except Exception as e:
            logger.error(f"读取Excel文件失败: {str(e)}")
            return None
    
    def get_sheet_names(self, file_path):
        """
        获取Excel文件的所有工作表名称
        
        参数:
            file_path (str): Excel文件路径
            
        返回:
            list: 工作表名称列表，失败返回None
        """
        try:
            wb = openpyxl.load_workbook(file_path, read_only=True)
            return wb.sheetnames
        except Exception as e:
            logger.error(f"获取工作表名称失败: {str(e)}")
            return None
    
    def create_template_excel(self, output_path, template_type='amazon'):
        """
        创建模板Excel文件
        
        参数:
            output_path (str): 输出文件路径
            template_type (str): 模板类型
            
        返回:
            bool: 创建是否成功
        """
        try:
            # 确保输出目录存在
            ensure_dir_exists(os.path.dirname(output_path))
            
            # 创建新的Excel工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            
            if template_type == 'amazon':
                ws.title = "亚马逊图片模板"
                # Amazon模板列标题
                columns = [
                    "ASIN", "原文件名", "MSKU", "SKU", "main_image_url", 
                    "other_image_url1", "other_image_url2", "other_image_url3", 
                    "other_image_url4", "other_image_url5", "other_image_url6", 
                    "other_image_url7", "other_image_url8", "swatch_image_url"
                ]
            else:
                ws.title = "通用模板"
                columns = ["文件名", "URL", "类型", "备注"]
            
            # 添加标题行
            ws.append(columns)
            
            # 设置标题行格式
            for cell in ws[1]:
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
            
            # 保存文件
            wb.save(output_path)
            logger.info(f"成功创建模板文件: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建模板文件失败: {str(e)}")
            return False

# 简化版本的保存函数
def save_to_amazon_excel_minimal(data_dict, output_path, task_list_info=None):
    """
    简化版本的Amazon Excel保存函数
    
    参数:
        data_dict (dict): 图片数据字典
        output_path (str): 输出路径
        task_list_info (dict): 任务列表信息
        
    返回:
        bool: 保存是否成功
    """
    processor = ExcelProcessorMinimal()
    return processor.save_amazon_mapping(data_dict, output_path, task_list_info) 